import { useState, useRef, useEffect } from "react";

interface TerminalLoginProps {
  onLogin: (password: string) => void;
}

const TerminalLogin = ({ onLogin }: TerminalLoginProps) => {
  const [password, setPassword] = useState("");
  const [showCursor, setShowCursor] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Focus on input when component mounts
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Cursor blinking effect
    const interval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 530);

    return () => clearInterval(interval);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password.trim()) {
      onLogin(password);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  return (
    <div className="min-h-screen bg-terminal-bg text-terminal-text font-mono flex flex-col justify-center items-start p-6 md:p-12">
      <div className="w-full max-w-4xl">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center gap-2 text-lg md:text-xl">
            <span className="text-terminal-text">&gt; Enter password:</span>
            <input
              ref={inputRef}
              type="password"
              value={password}
              onChange={handleInputChange}
              className="terminal-input bg-transparent border-none outline-none text-terminal-text caret-transparent flex-1 text-lg md:text-xl"
              autoFocus
              autoComplete="off"
            />
            <span className={`text-terminal-cursor ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity duration-100`}>
              █
            </span>
          </div>
          
          <div className="text-sm md:text-base text-terminal-text/70 mt-8">
            <p>Press Enter to continue...</p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TerminalLogin;