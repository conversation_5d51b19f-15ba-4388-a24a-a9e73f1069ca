import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

const EventPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    participants: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Basic validation
    if (!formData.name || !formData.email || !formData.participants) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }

    // Simulate form submission (replace with actual email functionality when Supabase is connected)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "RSVP Submitted!",
        description: "Thank you for your response. We'll be in touch soon.",
      });
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        participants: "",
        message: ""
      });
    } catch (error) {
      toast({
        title: "Submission Error",
        description: "There was an issue submitting your RSVP. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-event-bg text-event-text smooth-scroll">
      {/* Hero Section - Exhibition Style Layout */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-16 items-center min-h-screen">
            {/* Left Side - Main Text */}
            <div className="lg:col-span-6 space-y-6 lg:space-y-8">
              <div className="space-y-4">
                <h1 className="display-large text-event-text">
                  CELEBRATED
                </h1>
                <h2 className="display-large text-event-text">
                  IN
                </h2>
              </div>
              <div className="space-y-6 lg:space-y-8 mt-12 lg:mt-16">
                <p className="body-large text-event-text/90 max-w-md">
                  Join us for an extraordinary evening of culture and connection
                </p>
                <div className="space-y-2">
                  <p className="text-lg md:text-xl font-light tracking-wide text-event-text/80">
                    December 15, 2024
                  </p>
                  <p className="text-lg md:text-xl font-light tracking-wide text-event-text/80">
                    19:00 – 23:00
                  </p>
                </div>
              </div>
            </div>

            {/* Right Side - Vertical Text */}
            <div className="lg:col-span-6 flex justify-center lg:justify-end items-center">
              <div className="relative">
                <h2 className="text-vertical display-hero text-event-text opacity-90 lg:block hidden">
                  TÜBINGEN
                </h2>
                <h2 className="display-large text-event-text lg:hidden text-center">
                  TÜBINGEN
                </h2>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="w-px h-16 bg-event-text/30"></div>
        </div>
      </section>

      {/* Event Details Section */}
      <section className="py-24 lg:py-32">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-24">
              {/* Left Column - Event Info */}
              <div className="space-y-12">
                <div>
                  <h3 className="display-small mb-8">
                    Details
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-xl font-light tracking-wide mb-3 text-event-text/80">Location</h4>
                      <p className="body-large text-event-text/90 leading-relaxed">
                        Kulturzentrum Tübingen<br/>
                        Wilhelmstraße 30<br/>
                        72074 Tübingen
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="text-xl font-light tracking-wide mb-3 text-event-text/80">About</h4>
                      <p className="body-large text-event-text/90 leading-relaxed">
                        An extraordinary evening celebrating culture, connection, and community. Join us for an unforgettable experience that brings together art, conversation, and shared moments.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Additional Info */}
              <div className="space-y-12">
                <div>
                  <h3 className="display-small mb-8">
                    Experience
                  </h3>
                  <div className="space-y-6">
                    <p className="body-large text-event-text/90 leading-relaxed">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation.
                    </p>
                    <p className="body-large text-event-text/90 leading-relaxed">
                      Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* RSVP Section */}
      <section className="py-24 lg:py-32 border-t border-event-text/10">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="max-w-3xl mx-auto">
            <h3 className="display-medium text-center mb-16">
              Reserve Your Place
            </h3>
            
            <div className="bg-event-form-bg/50 backdrop-blur-sm border border-event-text/10 rounded-sm p-8 lg:p-12">
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <Label htmlFor="name" className="text-event-text text-base tracking-wide">
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="bg-event-bg/30 border-event-text/20 text-event-text placeholder:text-event-text/40 focus:border-event-text/60 h-12 text-base tracking-wide"
                      placeholder="Enter your full name"
                    />
                  </div>
                  
                  <div className="space-y-3">
                    <Label htmlFor="email" className="text-event-text text-base tracking-wide">
                      Email Address *
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="bg-event-bg/30 border-event-text/20 text-event-text placeholder:text-event-text/40 focus:border-event-text/60 h-12 text-base tracking-wide"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <Label htmlFor="participants" className="text-event-text text-base tracking-wide">
                    Number of Guests *
                  </Label>
                  <Input
                    id="participants"
                    name="participants"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.participants}
                    onChange={handleInputChange}
                    required
                    className="bg-event-bg/30 border-event-text/20 text-event-text placeholder:text-event-text/40 focus:border-event-text/60 h-12 text-base tracking-wide max-w-xs"
                    placeholder="1"
                  />
                </div>
                
                <div className="space-y-3">
                  <Label htmlFor="message" className="text-event-text text-base tracking-wide">
                    Additional Notes
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="bg-event-bg/30 border-event-text/20 text-event-text placeholder:text-event-text/40 focus:border-event-text/60 resize-none text-base tracking-wide"
                    placeholder="Special requirements, dietary preferences, etc."
                  />
                </div>
                
                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-event-text text-event-bg hover:bg-event-text/90 transition-colors text-base py-4 font-light tracking-widest uppercase"
                  >
                    {isSubmitting ? "Processing..." : "Confirm Attendance"}
                  </Button>
                </div>
              </form>
              
              <div className="mt-8 text-center space-y-2">
                <p className="text-sm text-event-text/50 tracking-wide">
                  * Required information
                </p>
                <p className="text-xs text-event-text/40 tracking-wide">
                  Note: Full email functionality requires backend integration
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 border-t border-event-text/10">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="text-center">
            <p className="text-event-text/50 tracking-wider text-sm">
              For inquiries: <EMAIL>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default EventPage;