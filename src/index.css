@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design system for two-stage event website */
@import url('https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* Terminal theme colors */
    --terminal-bg: 0 0% 0%;
    --terminal-text: 0 0% 100%;
    --terminal-cursor: 0 0% 100%;
    
    /* Event theme colors - refined dark blue from reference */
    --event-bg: 221 44% 20%;
    --event-text: 0 0% 100%;
    --event-accent: 221 44% 30%;
    --event-form-bg: 221 44% 15%;
    
    /* Default system colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221 44% 20%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221 44% 30%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 44% 20%;
    --radius: 0.5rem;
    
    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Terminal cursor blinking animation */
  .terminal-cursor {
    animation: blink 1s infinite;
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  /* Terminal input styling */
  .terminal-input {
    @apply bg-transparent border-none outline-none text-white font-mono caret-white;
  }
  
  /* Event page scroll */
  .smooth-scroll {
    scroll-behavior: smooth;
  }
  
  /* Event layout spacing */
  .event-container {
    @apply max-w-4xl mx-auto px-6 py-12;
  }
  
  /* Typography scale - refined for exhibition aesthetic */
  .display-hero {
    @apply text-7xl md:text-9xl lg:text-[12rem] font-light tracking-[0.2em] leading-none;
  }
  
  .display-large {
    @apply text-5xl md:text-7xl lg:text-8xl font-light tracking-[0.15em] leading-tight;
  }
  
  .display-medium {
    @apply text-3xl md:text-4xl lg:text-5xl font-light tracking-[0.1em] leading-tight;
  }
  
  .display-small {
    @apply text-xl md:text-2xl lg:text-3xl font-light tracking-[0.05em] leading-relaxed;
  }
  
  .body-large {
    @apply text-lg md:text-xl font-light leading-relaxed tracking-wide;
  }
  
  /* Vertical text styling */
  .text-vertical {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}
