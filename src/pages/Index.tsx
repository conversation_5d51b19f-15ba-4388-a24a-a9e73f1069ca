import { useState } from "react";
import TerminalLogin from "@/components/TerminalLogin";
import EventPage from "@/components/EventPage";

const Index = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const handleLogin = (password: string) => {
    // Simple password check - in production, this should be more secure
    if (password === "tubingen2024" || password === "event" || password === "celebrate") {
      setIsTransitioning(true);
      
      // Smooth transition to event page
      setTimeout(() => {
        setIsLoggedIn(true);
        setIsTransitioning(false);
      }, 500);
    } else {
      // Show error - could add toast notification here
      console.log("Invalid password");
    }
  };

  if (isTransitioning) {
    return (
      <div className="min-h-screen bg-terminal-bg flex items-center justify-center">
        <div className="text-terminal-text font-mono text-xl">
          Accessing event information...
        </div>
      </div>
    );
  }

  if (isLoggedIn) {
    return <EventPage />;
  }

  return <TerminalLogin onLogin={handleLogin} />;
};

export default Index;
